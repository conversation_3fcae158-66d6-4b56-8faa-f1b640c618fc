<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.0</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.pugwoo</groupId>
	<artifactId>weixin-msg</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>weixin-msg</name>

	<properties>
		<java.version>21</java.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.xerial</groupId>
			<artifactId>sqlite-jdbc</artifactId>
			<version>3.47.1.0</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
			<version>5.3.39</version>
		</dependency>

		<dependency>
			<groupId>com.pugwoo</groupId>
			<artifactId>nimble-orm</artifactId>
			<version>1.8.0</version>
		</dependency>
		<dependency>
			<groupId>com.pugwoo</groupId>
			<artifactId>woo-utils</artifactId>
			<version>1.3.12</version>
		</dependency>

		<!-- 这个是官方的java版本的protobuf库，必须 -->
		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
			<version>4.29.1</version>
		</dependency>

		<!-- LZ4压缩解压缩库，用于解析CompressContent -->
		<dependency>
			<groupId>org.lz4</groupId>
			<artifactId>lz4-java</artifactId>
			<version>1.8.0</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>dev.brachtendorf</groupId>
			<artifactId>JImageHash</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
