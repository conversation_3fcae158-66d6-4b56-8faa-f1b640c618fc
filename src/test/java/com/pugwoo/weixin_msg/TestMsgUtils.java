package com.pugwoo.weixin_msg;

import com.pugwoo.weixin_msg.dto.ContactDTO;
import com.pugwoo.weixin_msg.dto.MsgDTO;
import com.pugwoo.weixin_msg.utils.MsgUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * 用于测试MsgUtils的各项功能
 */
public class TestMsgUtils {

    @Test
    public void testParse() {
        List<MsgDTO> msgs = MsgUtils.parseMsg("E:\\weixin-msg\\User", "24507125117@chatroom",
                false, null);
        System.out.println(msgs.size());
    }

    @Test
    public void testParseMedia() {
        List<MsgDTO> msgs = MsgUtils.parseMsg("E:\\weixin-msg\\User", "24507125117@chatroom",
                true, ListUtils.of(4135175694025292375L));
        ListUtils.sortAscNullFirst(msgs, MsgDTO::getMsgTime);
        for (MsgDTO msg : msgs) {
            System.out.println(msg.getMsgTime() + ",msgSvrID:" + msg.getMsgSvrID()
                    + ",type:" + msg.getType() + ",filePath:" + msg.getFilePath()
                    + ", thumb:" + msg.getThumbnailPath());
        }

        // 统计图片中，有filePath的数量和有thumb的数量
        System.out.println("total:" + msgs.size() + ",filePath:" +
                ListUtils.filter(msgs, o -> o.getFilePath() != null).size()
                + ",thumb:" + ListUtils.filter(msgs, o -> o.getThumbnailPath() != null).size()
                + ",none:" + ListUtils.filter(msgs, o -> o.getFilePath() == null && o.getThumbnailPath() == null).size());
    }

    @Test
    public void testGetContacts() {
        // 测试获取所有联系人
        List<ContactDTO> allContacts = MsgUtils.getContacts("E:\\weixin-msg\\User", null);
        System.out.println("总联系人或群数量: " + allContacts.size());

        // allContacts = ListUtils.filter(allContacts, o -> o.getNickName().contains("豆豆"));
        
        // 显示前5个联系人
        for (int i = 0; i < Math.min(5, allContacts.size()); i++) {
            ContactDTO contact = allContacts.get(i);
            System.out.println("联系人: " + contact.getUserName() + " - " + contact.getNickName());
        }
        
        // 测试通配符查询
        List<ContactDTO> wildcardContacts = MsgUtils.getContacts("E:\\weixin-msg\\User", "*chat*");
        System.out.println("包含'chat'的联系人数量: " + wildcardContacts.size());
        
        // 测试精确查询（如果有特定联系人）
        List<ContactDTO> specificContacts = MsgUtils.getContacts("E:\\weixin-msg\\User", "24507125117@chatroom");
        System.out.println("特定联系人查询结果数量: " + specificContacts.size());
        if (!specificContacts.isEmpty()) {
            System.out.println("找到联系人: " + specificContacts.get(0).getNickName());
        }
    }

    @Test
    public void testParseFileType() {
        // 测试解析包含文件类型的消息
        List<MsgDTO> msgs = MsgUtils.parseMsg("E:\\weixin-msg\\User", "22234014317@chatroom",
                false, null);

        // 过滤出文件类型的消息
        List<MsgDTO> fileMsgs = ListUtils.filter(msgs, msg -> msg.getType() == com.pugwoo.weixin_msg.enums.MsgTypeEnum.FILE);

        System.out.println("总消息数: " + msgs.size());
        System.out.println("文件类型消息数: " + fileMsgs.size());

        // 显示前几个文件消息的详细信息
        for (int i = 0; i < Math.min(5, fileMsgs.size()); i++) {
            MsgDTO fileMsg = fileMsgs.get(i);
            System.out.println("文件消息 " + (i + 1) + ":");
            System.out.println("  时间: " + fileMsg.getMsgTime());
            System.out.println("  消息ID: " + fileMsg.getMsgSvrID());
            System.out.println("  内容: " + fileMsg.getContent());
            System.out.println("  文件路径: " + fileMsg.getFilePath());
            System.out.println("  文件UUID: " + fileMsg.getFileUuid());
            System.out.println("  缩略图路径: " + fileMsg.getThumbnailPath());
            
            // 显示额外的文件路径信息（如果有多个文件）
            if (fileMsg.getFilePaths() != null && !fileMsg.getFilePaths().isEmpty()) {
                System.out.println("  额外文件路径数量: " + fileMsg.getFilePaths().size());
                for (int j = 0; j < fileMsg.getFilePaths().size(); j++) {
                    System.out.println("    文件" + (j + 2) + ": " + fileMsg.getFilePaths().get(j));
                }
            }
            System.out.println();
        }

        // 统计有文件路径的文件消息数量
        long filePathCount = fileMsgs.stream().filter(msg -> msg.getFilePath() != null).count();
        System.out.println("有文件路径的文件消息数: " + filePathCount);
        
        // 统计有多个文件路径的文件消息数量
        long multipleFilePathCount = fileMsgs.stream().filter(msg -> 
            msg.getFilePaths() != null && !msg.getFilePaths().isEmpty()).count();
        System.out.println("有多个文件路径的文件消息数: " + multipleFilePathCount);
    }

}
