package com.pugwoo.weixin_msg;

import com.pugwoo.weixin_msg.utils.FileUtils;
import org.junit.jupiter.api.Test;

public class TestFileUtils {

    @Test
    public void testImageSame() {
        String file1 = "D:\\Users\\桌面\\819013c1df50e74e7c00729257223cd5.dat";
        String file2 = "D:\\Users\\桌面\\21eb9a8ed2bcfde8edc9572bc803b21d.dat";

        System.out.println("Quick check result: " + FileUtils.isSameImage(file1, file2));
        // System.out.println(ImageUtils.similarityScore(new File(file1), new File(file2)));
    }

    @Test
    public void testVideoSame() {
        String file1 = "D:\\Users\\桌面\\9b8b2f92d42050840e4b15911e88517d.mp4";
        String file2 = "D:\\Users\\桌面\\9b8b2f92d42050840e4b15911e88517d(1).mp4";

        System.out.println("result:" + FileUtils.isSameFileQuickCheck(file1, file2));
    }

}
