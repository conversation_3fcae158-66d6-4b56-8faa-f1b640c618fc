package com.pugwoo.weixin_msg;

import com.pugwoo.weixin_msg.dto.MsgDTO;
import com.pugwoo.weixin_msg.enums.MsgTypeEnum;
import com.pugwoo.weixin_msg.utils.FileUtils;
import com.pugwoo.weixin_msg.utils.MsgUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 测试比较一下PC的聊天记录和手机的聊天记录是否相同
 */
public class TestComparePCAndPhone {

    private String pcPath = "F:/weixin_CUR/User";
    private String phonePath = "F:/weixin_IQOO/User";

    @Test
    public void testCompare() {
        System.out.print("开始比较PC和手机的聊天记录，");
        System.out.print("PC路径: " + pcPath);
        System.out.println("，手机路径: " + phonePath);

        testCompare("24507125117@chatroom", "豆豆国际主题托管家庭群", true); // 豆豆托班
        // testCompare("22234014317@chatroom", true); // 豆豆上小学
    }

    public void testCompare(String groupId, String groupName, boolean isOnlyMedia) {

        System.out.println("==================================");
        System.out.println("群组ID: " + groupId + "(" + groupName + ")，只处理媒体文件: " + isOnlyMedia);

        System.out.print("正在解析PC端聊天记录...");
        System.out.flush();
        List<MsgDTO> pcMessages = MsgUtils.parseMsg(pcPath, groupId, isOnlyMedia, null);
        System.out.println("PC端消息数量: " + pcMessages.size());
        System.out.print("正在解析手机端聊天记录...");
        System.out.flush();
        List<MsgDTO> phoneMessages = MsgUtils.parseMsg(phonePath, groupId, isOnlyMedia, null);
        System.out.println("手机端消息数量: " + phoneMessages.size());

        // 创建消息映射，以msgSvrID为key
        Map<Long, MsgDTO> pcMsgMap = ListUtils.toMap(pcMessages, MsgDTO::getMsgSvrID, o -> o);
        Map<Long, MsgDTO> phoneMsgMap = ListUtils.toMap(phoneMessages, MsgDTO::getMsgSvrID, o -> o);

        System.out.println("开始比较分析...");

        // 统计信息
        int totalCompared = 0;
        int filePathDifferent = 0;
        int thumbnailPathDifferent = 0;
        int onlyInPC = 0;
        int onlyInPhone = 0;
        int bothHaveFiles = 0;
        int bothMissingFiles = 0;

        // 获取所有唯一的msgSvrID
        Set<Long> allMsgIds = new HashSet<>();
        allMsgIds.addAll(pcMsgMap.keySet());
        allMsgIds.addAll(phoneMsgMap.keySet());

        for (Long msgId : allMsgIds) {
            MsgDTO pcMsg = pcMsgMap.get(msgId);
            MsgDTO phoneMsg = phoneMsgMap.get(msgId);

            if (pcMsg == null) {
                onlyInPhone++;
                System.err.println("【仅手机端存在】msgSvrID: " + msgId +
                    ", 时间: " + phoneMsg.getMsgTime() +
                    ", 类型: " + phoneMsg.getType() +
                    ", 文件路径: " + phoneMsg.getFilePath() +
                    ", 缩略图路径: " + phoneMsg.getThumbnailPath());
                continue;
            }

            if (phoneMsg == null) {
                onlyInPC++;
                System.err.println("【仅PC端存在】msgSvrID: " + msgId +
                    ", 时间: " + pcMsg.getMsgTime() +
                    ", 类型: " + pcMsg.getType() +
                    ", 文件路径: " + pcMsg.getFilePath() +
                    ", 缩略图路径: " + pcMsg.getThumbnailPath());
                continue;
            }

            // 两端都存在的消息
            totalCompared++;

            boolean fileSame =
                    pcMsg.getType() == MsgTypeEnum.IMAGE ?
                            FileUtils.isSameImage(pcMsg.getFilePath(), pcMsg.getFilePath()) :
                            FileUtils.isSameFileQuickCheck(pcMsg.getFilePath(), phoneMsg.getFilePath());
            boolean thumbnailSame =
                    pcMsg.getType() == MsgTypeEnum.IMAGE ?
                            FileUtils.isSameImage(pcMsg.getThumbnailPath(), phoneMsg.getThumbnailPath()) :
                            FileUtils.isSameFileQuickCheck(pcMsg.getThumbnailPath(), phoneMsg.getThumbnailPath());

            // 统计文件存在情况
            boolean pcHasFile = pcMsg.getFilePath() != null || pcMsg.getThumbnailPath() != null;
            boolean phoneHasFile = phoneMsg.getFilePath() != null || phoneMsg.getThumbnailPath() != null;

            if (pcHasFile && phoneHasFile) {
                bothHaveFiles++;
            } else if (!pcHasFile && !phoneHasFile) {
                bothMissingFiles++;
            }

            if (!fileSame) {
                filePathDifferent++;
                System.out.println("【文件不同】msgSvrID: " + msgId +
                    ", 时间: " + pcMsg.getMsgTime() +
                    ", 类型: " + pcMsg.getType());
                System.out.println("  PC文件: " + pcMsg.getFilePath());
                System.out.println("  手机文件: " + phoneMsg.getFilePath());
            }

            if (!thumbnailSame) {
                thumbnailPathDifferent++;
                System.out.println("【缩略图不同】msgSvrID: " + msgId +
                    ", 时间: " + pcMsg.getMsgTime() +
                    ", 类型: " + pcMsg.getType());
                System.out.println("  PC缩略图: " + pcMsg.getThumbnailPath());
                System.out.println("  手机缩略图: " + phoneMsg.getThumbnailPath());
            }

            // 如果路径都不同，输出详细信息
            if (!fileSame || !thumbnailSame) {
                System.out.println("  消息内容: " + (pcMsg.getContent().length() > 100 ?
                    pcMsg.getContent().substring(0, 100) + "..." : pcMsg.getContent()));
                System.out.println("  -------------------------");
            }
        }

        // 输出统计结果
        System.out.println("比较结果统计:");
        System.out.println("总消息ID数量: " + allMsgIds.size());
        System.out.println("两端都存在的消息: " + totalCompared);
        System.out.println("仅PC端存在: " + onlyInPC);
        System.out.println("仅手机端存在: " + onlyInPhone);
        System.out.println("文件不同的消息: " + filePathDifferent);
        System.out.println("缩略图不同的消息: " + thumbnailPathDifferent);
        System.out.println("两端都有文件的消息: " + bothHaveFiles);
        System.out.println("两端都缺失文件的消息: " + bothMissingFiles);

        System.out.println("比较完成!");
    }

}
