package com.pugwoo.weixin_msg.dto;

import com.pugwoo.weixin_msg.enums.MsgDiffTypeEnum;
import lombok.Data;

import java.util.List;

@Data
public class MsgCompareResultDTO {

    /**
     * 最终是否相等
     */
    private boolean isEquals;

    /**
     * 差别的地方，用文字方式描述
     */
    private List<Difference> differences;

    @Data
    public static class Difference {

        private MsgDiffTypeEnum diffType;

        private String description;

    }

}
