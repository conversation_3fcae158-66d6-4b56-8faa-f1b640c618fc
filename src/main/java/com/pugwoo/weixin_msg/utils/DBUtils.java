package com.pugwoo.weixin_msg.utils;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.sqlite.SQLiteDataSource;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class DBUtils {

    /**
     * 打开一个sqlite数据库
     * @param sqliteDBPath sqlite数据库文件路径
     * @return DBHelper实例
     */
    public static DBHelper getDBHelper(String sqliteDBPath) {
        if (!sqliteDBPath.startsWith("jdbc:sqlite:")) {
            sqliteDBPath = "jdbc:sqlite:" + sqliteDBPath;
        }
        SQLiteDataSource dataSource = new SQLiteDataSource();
        dataSource.setUrl(sqliteDBPath);
        DBHelper dbHelper = new SpringJdbcDBHelper(new JdbcTemplate(dataSource));
        dbHelper.setSlowSqlWarningValve(60000);
        return dbHelper;
    }

    /**
     * 列举出指定目录下满足正则表达式的文件
     * @param baseDir 指定目录
     * @param fileRegex 文件名称正则表达式
     * @return 返回绝对路径
     */
    public static List<String> listDBFile(String baseDir, String fileRegex) {
        File[] files = new File(baseDir).listFiles();
        if (files == null) {
            return new ArrayList<>();
        }
        List<String> result = new ArrayList<>();
        for (File file : files) {
            if (file.getName().matches(fileRegex)) {
                result.add(file.getAbsolutePath());
            }
        }
        return result;
    }

}
