package com.pugwoo.weixin_msg.utils;

import com.pugwoo.wooutils.string.StringTools;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.FileTime;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class FileUtils {

    public static void copy(String sourcePath, String destPath) throws IOException{
        Path source = Paths.get(sourcePath);
        Path destination = Paths.get(destPath);

        Files.createDirectories(destination.getParent());
        Files.copy(source, destination, StandardCopyOption.COPY_ATTRIBUTES, StandardCopyOption.REPLACE_EXISTING);
        FileTime lastModifiedTime = Files.getLastModifiedTime(source);
        Files.setLastModifiedTime(destination, lastModifiedTime);
    }

    public static void delete(String path) throws IOException {
        Files.deleteIfExists(Paths.get(path));
    }

    public static void move(String path1, String path2) throws IOException {
        Files.move(Paths.get(path1), Paths.get(path2), StandardCopyOption.REPLACE_EXISTING);
    }

    @SneakyThrows
    public static List<String> listFiles(String dir) {
        List<String> filePaths = new ArrayList<>();
        if (!FileUtils.isExist(dir)) {
            return filePaths;
        }
        Files.walk(Paths.get(dir))
                .filter(Files::isRegularFile)
                .forEach(path -> filePaths.add(path.toAbsolutePath().toString()));
        return filePaths;
    }

    public static boolean isExist(String absolutePath) {
        return Files.exists(Paths.get(absolutePath));
    }

    public static boolean isFile(String absolutePath) {
        return Files.isRegularFile(Paths.get(absolutePath));
    }

    /**
     * 比较图片文件是否相同，要求文件大小差别在1%之内，然后用图像比较，差别也在1%以内，则认为是相等
     */
    public static boolean isSameImage(String sourcePath, String destPath) {
        if (StringTools.isBlank(sourcePath) && StringTools.isBlank(destPath)) {
            return true;
        }
        if (StringTools.isBlank(sourcePath) || StringTools.isBlank(destPath)) {
            return false;
        }

        File sourceFile = new File(sourcePath);
        File destFile = new File(destPath);
        // 检查文件是否存在
        if (!sourceFile.exists() || !destFile.exists()) {
            return false;
        }

        long sourceFileLength = sourceFile.length();
        long destFileLength = destFile.length();

        // 如果任一文件为空，则必须都为空才相同
        if (sourceFileLength == 0 || destFileLength == 0) {
            return sourceFileLength == destFileLength;
        }
        if (Math.abs(sourceFileLength - destFileLength) > sourceFileLength / 100) {
            return false;
        }

        // 再检查图像差异
        try {
            return ImageUtils.similarityScore(new File(sourcePath), new File(destPath)) < 0.01;
        } catch (Exception e) {
            // 如果图像比较失败，返回false表示不相同
            log.error("compare two images failed", e);
            return false;
        }
    }

    /**
     * 仅适用于微信比较视频是否相等，判断条件：
     *
     * 1）文件的大小相差256字节以内
     * 2）如果文件大于2M，要求前1M大小完全相同
     * 3）如果文件小于等于2M，要求前50%大小完全相同
     *
     * @param sourcePath 源文件路径
     * @param destPath 目标文件路径
     * @return 如果文件相同（允许1%差异）返回true，否则返回false
     */
    public static boolean isSameFileQuickCheck(String sourcePath, String destPath) {
        if (StringTools.isBlank(sourcePath) && StringTools.isBlank(destPath)) {
            return true;
        }
        if (StringTools.isBlank(sourcePath) || StringTools.isBlank(destPath)) {
            return false;
        }

        File sourceFile = new File(sourcePath);
        File destFile = new File(destPath);
        // 检查文件是否存在
        if (!sourceFile.exists() || !destFile.exists()) {
            return false;
        }

        long sourceFileLength = sourceFile.length();
        long destFileLength = destFile.length();

        // 如果任一文件为空，则必须都为空才相同
        if (sourceFileLength == 0 || destFileLength == 0) {
            return sourceFileLength == destFileLength;
        }
        if (Math.abs(sourceFileLength - destFileLength) > 256) {
            return false;
        }

        // 计算差异
        long maxFileLength = Math.max(sourceFileLength, destFileLength);
        long compareLength = 0;
        if (maxFileLength >= 6 * 1024 * 1024) {
            compareLength = 3 * 1024 * 1024;
        } else {
            compareLength = maxFileLength / 2;
        }

        try (FileInputStream sourceStream = new FileInputStream(sourceFile);
             FileInputStream destStream = new FileInputStream(destFile)) {

            return compareFullContent(sourceStream, destStream, (int)compareLength, 0);
        } catch (IOException e) {
            // 发生IO异常时认为文件不同
            return false;
        }
    }

    private static boolean compareFullContent(FileInputStream sourceStream, FileInputStream destStream,
                                            int length, long allowedDifference) throws IOException {
        byte[] sourceBuffer = new byte[length];
        byte[] destBuffer = new byte[length];

        int sourceRead = sourceStream.read(sourceBuffer);
        int destRead = destStream.read(destBuffer);

        if (sourceRead != destRead) {
            return Math.abs(sourceRead - destRead) <= allowedDifference;
        }

        // 计算不同字节数
        int differentBytes = 0;
        for (int i = 0; i < sourceRead; i++) {
            if (sourceBuffer[i] != destBuffer[i]) {
                differentBytes++;
                if (differentBytes > allowedDifference) {
                    return false;
                }
            }
        }

        return differentBytes <= allowedDifference;
    }

    /**
     * 计算文件内容的MD5值
     * @param filePath 文件路径
     * @return 文件的MD5哈希值（32位小写字符串），如果文件不存在或计算失败返回null
     */
    public static String calculateFileMD5(String filePath) {
        if (StringTools.isBlank(filePath)) {
            return null;
        }
        
        File file = new File(filePath);
        if (!file.exists() || !file.isFile()) {
            return null;
        }
        
        try (FileInputStream fis = new FileInputStream(file)) {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[8192];
            int bytesRead;
            
            while ((bytesRead = fis.read(buffer)) != -1) {
                md.update(buffer, 0, bytesRead);
            }
            
            byte[] md5Bytes = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : md5Bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (IOException | NoSuchAlgorithmException e) {
            log.error("计算文件MD5失败: " + filePath, e);
            return null;
        }
    }

}
