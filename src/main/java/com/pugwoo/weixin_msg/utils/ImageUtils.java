package com.pugwoo.weixin_msg.utils;

import dev.brachtendorf.jimagehash.hash.Hash;
import dev.brachtendorf.jimagehash.hashAlgorithms.HashingAlgorithm;
import dev.brachtendorf.jimagehash.hashAlgorithms.PerceptiveHash;
import lombok.SneakyThrows;

import java.io.File;

public class ImageUtils {

    @SneakyThrows
    public static double similarityScore(File img0, File img1) {
        HashingAlgorithm hasher = new PerceptiveHash(32);

        Hash hash0 = hasher.hash(img0);
        Hash hash1 = hasher.hash(img1);

        return hash0.normalizedHammingDistance(hash1);
    }

}
