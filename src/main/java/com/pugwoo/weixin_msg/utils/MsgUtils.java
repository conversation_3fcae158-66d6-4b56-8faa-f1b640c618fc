package com.pugwoo.weixin_msg.utils;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.weixin_msg.dto.ContactDTO;
import com.pugwoo.weixin_msg.dto.MsgDTO;
import com.pugwoo.weixin_msg.entity.ContactDO;
import com.pugwoo.weixin_msg.entity.MsgDO;
import com.pugwoo.weixin_msg.enums.MsgTypeEnum;
import com.pugwoo.weixin_msg.protobuf.Msg;
import com.pugwoo.wooutils.string.StringTools;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class MsgUtils {

    /**
     * 获取联系人或群的信息
     * @param decryptMsgDir wechatDataBackup生成的User文件夹的位置【必须】
     * @param name 可以搜索id或名称或昵称等，可选，如果没填表示全部，支持通配符*?
     */
    @SneakyThrows
    public static List<ContactDTO> getContacts(String decryptMsgDir, String name) {
        List<ContactDTO> result = new ArrayList<>();
        
        // 查找MicroMsg.db文件
        String micromsgDbPath = decryptMsgDir + "\\pugwoo\\Msg\\MicroMsg.db";
        File micromsgDbFile = new File(micromsgDbPath);
        if (!micromsgDbFile.exists()) {
            log.warn("MicroMsg.db文件不存在: {}", micromsgDbPath);
            return result;
        }
        
        try {
            DBHelper dbHelper = DBUtils.getDBHelper(micromsgDbPath);
            
            String whereClause = "where 1=1";
            List<Object> params = new ArrayList<>();
            
            if (StringTools.isNotBlank(name)) {
                if (name.contains("*") || name.contains("?")) { // 支持通配符查询
                    String likePattern = name.replace("*", "%").replace("?", "_");
                    whereClause += " and (UserName like ? or NickName like ? or Remark like ?)";
                    params.add(likePattern);
                    params.add(likePattern);
                    params.add(likePattern);
                } else { // 精确匹配
                    whereClause += " and (UserName = ? or NickName = ? or Remark = ?)";
                    params.add(name);
                    params.add(name);
                    params.add(name);
                }
            }
            
            List<ContactDO> contacts;
            if (params.isEmpty()) {
                contacts = dbHelper.getAll(ContactDO.class, whereClause);
            } else {
                contacts = dbHelper.getAll(ContactDO.class, whereClause, params.toArray());
            }
            
            // 转换为DTO
            for (ContactDO contact : contacts) {
                ContactDTO dto = new ContactDTO();
                dto.setUserName(contact.getUserName());
                
                // 显示名称优先级：备注 > 昵称 > 用户名
                String displayName = contact.getRemark();
                if (StringTools.isBlank(displayName)) {
                    displayName = contact.getNickName();
                }
                if (StringTools.isBlank(displayName)) {
                    displayName = contact.getUserName();
                }
                dto.setNickName(displayName);
                
                result.add(dto);
            }
            
        } catch (Exception e) {
            log.error("查询联系人信息时发生错误", e);
        }
        
        return result;
    }


    /**
     * 解析由wechatDataBackup生成的文件中的消息
     *
     * @param decryptMsgDir wechatDataBackup生成的User文件夹的位置【必须】
     * @param groupId 群的id，例如24507125117@chatroom
     * @param isOnlyMedia 是否只处理图片、视频、文件
     * @param ignoredMsgIds 指定的忽略不处理的消息id
     * @return 处理后的消息
     */
    @SneakyThrows
    public static List<MsgDTO> parseMsg(String decryptMsgDir, String groupId, boolean isOnlyMedia, List<Long> ignoredMsgIds) {
        List<MsgDTO> result = new ArrayList<>();

        List<String> dbFiles = DBUtils.listDBFile(decryptMsgDir + "\\pugwoo\\Msg\\Multi", "^MSG.*\\.db");
        for (String dbPath : dbFiles) {
            DBHelper dbHelper = DBUtils.getDBHelper(dbPath);
            List<MsgDO> msgList = dbHelper.getAll(MsgDO.class, "where StrTalker=?", groupId);

            for (MsgDO msg : msgList) {
                if (ignoredMsgIds != null && ignoredMsgIds.contains(msg.getMsgSvrID())) {
                    continue;
                }

                MsgTypeEnum msgTypeEnum = transToMyType(msg.getType(), msg.getSubType());

                if (isOnlyMedia && !(msgTypeEnum == MsgTypeEnum.IMAGE || msgTypeEnum == MsgTypeEnum.VIDEO
                        || msgTypeEnum == MsgTypeEnum.FILE)) {
                    continue; // 只处理图片、视频、文件
                }

                MsgDTO msgDTO = new MsgDTO();
                result.add(msgDTO);
                msgDTO.setMsgTime(from(msg.getCreateTime()));
                msgDTO.setMsgSvrID(msg.getMsgSvrID());
                msgDTO.setContent(msg.getStrContent());
                msgDTO.setType(msgTypeEnum);

                // 最重要的，解析文件路径
                if (msgTypeEnum == MsgTypeEnum.IMAGE) {
                    parseImage(msgDTO, msg, decryptMsgDir);
                } else if (msgTypeEnum == MsgTypeEnum.VIDEO) {
                    parseVideo(msgDTO, msg, decryptMsgDir);
                } else if (msgTypeEnum == MsgTypeEnum.COMBINE) {
                    parseCombineMessage(msgDTO, msg, decryptMsgDir);
                } else if (msgTypeEnum == MsgTypeEnum.FILE) {
                    parseFile(msgDTO, msg, decryptMsgDir);
                }
            }
        }

        return result;
    }

    @SneakyThrows
    private static void parseVideo(MsgDTO msgDTO, MsgDO msg, String decryptMsgDir) {
        if (msg.getBytesExtra() != null && msg.getBytesExtra().length > 0) {
            Msg.MessageBytesExtra messageBytesExtra = Msg.MessageBytesExtra.parseFrom(msg.getBytesExtra());
            List<Msg.SubMessage2> message2List = messageBytesExtra.getMessage2List();

            boolean isVideoFound = false;
            for (Msg.SubMessage2 message2 : message2List) {
                String field2 = message2.getField2();
                if (field2.toLowerCase().endsWith(".mp4")) {
                    String path = decryptMsgDir + "\\" + field2;
                    // 检查文件是否存在，存在才算是真的存在
                    if (FileUtils.isExist(path)) {
                        isVideoFound = true;
                        msgDTO.setFilePath(path);
                        msgDTO.setFileUuid(getFileUuid(path));
                        break;
                    }
                }
            }

            if (!isVideoFound) { // 如果视频找不到，就找缩略图
                for (Msg.SubMessage2 message2 : message2List) {
                    String field2 = message2.getField2();
                    if (field2.toLowerCase().endsWith(".jpg")) {
                        String path = decryptMsgDir + "\\" + field2;
                        // 检查文件是否存在，存在才算是真的存在
                        if (FileUtils.isExist(path)) {
                            msgDTO.setThumbnailPath(path);
                            msgDTO.setFileUuid(getFileUuid(path));
                            break;
                        }
                    }
                }
            }
        } else {
            throw new RuntimeException("视频消息没有bytesExtra，msgSvrID=" + msg.getMsgSvrID());
        }
    }

    @SneakyThrows
    private static void parseImage(MsgDTO msgDTO, MsgDO msg, String decryptMsgDir) {
        if (msg.getBytesExtra() != null && msg.getBytesExtra().length > 0) {
            Msg.MessageBytesExtra messageBytesExtra = Msg.MessageBytesExtra.parseFrom(msg.getBytesExtra());
            List<Msg.SubMessage2> message2List = messageBytesExtra.getMessage2List();

            boolean isImageFound = false;
            for (Msg.SubMessage2 message2 : message2List) {
                String field2 = message2.getField2();
                if (field2.toLowerCase().endsWith(".dat") && field2.toLowerCase().contains("image")
                    && !field2.toLowerCase().contains("thumb")) {
                    String path = decryptMsgDir + "\\" + field2;
                    // 检查文件是否存在，存在才算是真的存在
                    if (FileUtils.isExist(path)) {
                        isImageFound = true;
                        msgDTO.setFilePath(path);
                        msgDTO.setFileUuid(getFileUuid(path));
                        break;
                    }
                }
            }
            if (!isImageFound) { // 如果图片找不到，就找缩略图
                for (Msg.SubMessage2 message2 : message2List) {
                    String field2 = message2.getField2();
                    if (field2.toLowerCase().endsWith(".dat") && field2.toLowerCase().contains("thumb")) {
                        String path = decryptMsgDir + "\\" + field2;
                        // 检查文件是否存在，存在才算是真的存在
                        if (FileUtils.isExist(path)) {
                            msgDTO.setThumbnailPath(path);
                            msgDTO.setFileUuid(getFileUuid(path));
                            break;
                        }
                    }
                }
            }
        } else {
            throw new RuntimeException("图片消息没有bytesExtra，msgSvrID=" + msg.getMsgSvrID());
        }
    }

    @SneakyThrows
    private static void parseFile(MsgDTO msgDTO, MsgDO msg, String decryptMsgDir) {
        if (msg.getBytesExtra() != null && msg.getBytesExtra().length > 0) {
            Msg.MessageBytesExtra messageBytesExtra = Msg.MessageBytesExtra.parseFrom(msg.getBytesExtra());
            List<Msg.SubMessage2> message2List = messageBytesExtra.getMessage2List();

            boolean isFileFound = false;
            Set<String> md5Set = new HashSet<>(); // 用于记录已添加文件的MD5值
            StringBuilder pathsAsString = new StringBuilder();
            
            for (Msg.SubMessage2 message2 : message2List) {
                String field2 = message2.getField2();
                if (StringTools.isNotBlank(field2) && !isRandomStr(field2)) {
                    String path = decryptMsgDir + "\\" + field2;
                    pathsAsString.append(path);

                    // 检查文件是否存在，存在才算是真的存在
                    if (FileUtils.isExist(path) && FileUtils.isFile(path)) {
                        // 计算文件内容的MD5值
                        String fileMd5 = FileUtils.calculateFileMD5(path);
                        // 如果MD5计算失败，则跳过此文件
                        if (fileMd5 == null) {
                            throw new RuntimeException("计算文件md5失败:" + path);
                        }
                        // 检查MD5是否已存在，如果已存在则跳过此文件
                        if (md5Set.contains(fileMd5)) {
                            log.debug("跳过重复文件，MD5: {}, 路径: {}", fileMd5, path);
                            continue;
                        }
                        // 添加MD5到集合中
                        md5Set.add(fileMd5);
                        
                        if (isFileFound) {
                            // 已经找到至少1个文件，剩余的文件放到filePaths中
                            if (msgDTO.getFilePaths() == null) {
                                msgDTO.setFilePaths(new ArrayList<>());
                            }
                            msgDTO.getFilePaths().add(path);
                        } else {
                            isFileFound = true;
                            msgDTO.setFilePath(path);
                        }
                    }
                }
            }

            if (!isFileFound) {
                // 如果在bytesExtra中找不到文件，可以尝试其他方式
                // 比如在特定的文件目录中查找
                log.warn("文件消息找不到对应的文件，msgSvrID=" + msg.getMsgSvrID() + ",paths:" + pathsAsString);
            }
        } else {
            throw new RuntimeException("文件消息没有bytesExtra，msgSvrID=" + msg.getMsgSvrID());
        }
    }

    private static String getFileUuid(String file) {
        return file.substring(file.lastIndexOf("\\") + 1, file.lastIndexOf("."));
    }

    private static MsgTypeEnum transToMyType(Integer type, Integer subType) {
        if (type == null) {
            return MsgTypeEnum.UNKNOWN;
        }
        if (type.equals(0)) {
            return MsgTypeEnum.TEXT;
        }
        if (type.equals(3)) {
            return MsgTypeEnum.IMAGE;
        }
        if (type.equals(43)) {
            return MsgTypeEnum.VIDEO;
        }
        if (type.equals(49) && subType != null && subType.equals(6)) {
            return MsgTypeEnum.FILE;
        }
        if (type.equals(49) && subType != null && subType.equals(19)) {
            return MsgTypeEnum.COMBINE;
        }
        return MsgTypeEnum.OTHERS;
    }

    private static LocalDateTime from(Long creatTime) {
        Instant instant = Instant.ofEpochSecond(creatTime);
        ZoneId zoneId = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zoneId);
    }

    /**
     * 解析合并转发消息
     * @param msgDTO 消息DTO
     * @param msg 原始消息
     * @param decryptMsgDir 解密消息目录
     */
    @SneakyThrows
    private static void parseCombineMessage(MsgDTO msgDTO, MsgDO msg, String decryptMsgDir) {
        msgDTO.setType(MsgTypeEnum.COMBINE);

        // 解析XML内容获取基本信息
        String xmlContent = msg.getStrContent();
        parseCombineXmlContent(msgDTO, xmlContent);

        // 解析CompressContent获取详细聊天记录
        if (msg.getCompressContent() != null && msg.getCompressContent().length > 0) {
            parseCombineCompressContent(msgDTO, msg.getCompressContent(), decryptMsgDir);
        }
    }

    /**
     * 解析合并转发消息的XML内容
     * @param msgDTO 消息DTO
     * @param xmlContent XML内容
     */
    private static void parseCombineXmlContent(MsgDTO msgDTO, String xmlContent) {
        try {
            String title = extractXmlValue(xmlContent, "title");
            String des = extractXmlValue(xmlContent, "des");

            String content;
            if (title != null && !title.trim().isEmpty()) {
                content = "合并转发: " + title;
            } else if (des != null && !des.trim().isEmpty()) {
                content = "合并转发: " + des;
            } else {
                content = "合并转发消息";
            }

            msgDTO.setContent(content);
        } catch (Exception e) {
            msgDTO.setContent("合并转发消息");
        }
    }

    /**
     * 解析合并转发消息的CompressContent
     * @param msgDTO 消息DTO
     * @param compressContent 压缩内容
     * @param decryptMsgDir 解密消息目录
     */
    private static void parseCombineCompressContent(MsgDTO msgDTO, byte[] compressContent, String decryptMsgDir) {
        try {
            // 使用LZ4解压缩
            String decompressedXml = decompressLZ4(compressContent);
            if (decompressedXml != null && !decompressedXml.trim().isEmpty()) {
                // 解析聊天记录
                String chatRecords = parseChatRecordsFromXml(decompressedXml);
                if (chatRecords != null && !chatRecords.trim().isEmpty()) {
                    String currentContent = msgDTO.getContent();
                    msgDTO.setContent(currentContent + "\n聊天记录摘要:\n" + chatRecords);
                }

                // 解析合并消息中的文件路径
                parseCombineFilePaths(msgDTO, decompressedXml, decryptMsgDir);
            }
        } catch (Exception e) {
            // 解析失败时不影响基本功能，保持原有内容
        }
    }

    /**
     * 使用LZ4解压缩数据
     * @param compressedData 压缩数据
     * @return 解压后的字符串
     */
    private static String decompressLZ4(byte[] compressedData) {
        try {
            if (compressedData == null) {
                return null;
            }
            byte[] decompressed = LZ4Utils.decompress(compressedData);
            return new String(decompressed, "UTF-8");
        } catch (Exception e) {
            log.error("decompressLZ4 fail", e);
            return null;
        }
    }

    /**
     * 从XML中解析聊天记录
     * @param xmlContent XML内容
     * @return 格式化的聊天记录
     */
    private static String parseChatRecordsFromXml(String xmlContent) {
        try {
            List<String> records = new ArrayList<>();

            // 首先尝试解析新格式的recordinfo结构
            if (xmlContent.contains("<recordinfo>")) {
                return parseRecordInfoFormat(xmlContent);
            }

            // 兼容旧格式的recorditem结构
            Pattern recordPattern = Pattern.compile("<recorditem[^>]*>(.*?)</recorditem>", Pattern.DOTALL);
            Matcher recordMatcher = recordPattern.matcher(xmlContent);

            int count = 0;
            int maxRecords = 5; // 最多显示5条记录

            while (recordMatcher.find() && count < maxRecords) {
                String recordContent = recordMatcher.group(1);

                String nickname = extractXmlValue(recordContent, "nickname");
                String content = extractXmlValue(recordContent, "content");
                String timeStr = extractXmlValue(recordContent, "time");

                if (nickname != null && content != null) {
                    String timeDisplay = "";
                    if (timeStr != null && timeStr.matches("\\d+")) {
                        try {
                            long timestamp = Long.parseLong(timeStr);
                            LocalDateTime time = from(timestamp);
                            timeDisplay = "[" + time + "] ";
                        } catch (Exception e) {
                            timeDisplay = "[" + timeStr + "] ";
                        }
                    }

                    records.add(timeDisplay + nickname + ": " + content);
                    count++;
                }
            }

            // 计算总记录数
            Matcher totalMatcher = recordPattern.matcher(xmlContent);
            int totalCount = 0;
            while (totalMatcher.find()) {
                totalCount++;
            }

            if (!records.isEmpty()) {
                StringBuilder result = new StringBuilder();
                for (String record : records) {
                    result.append(record).append("\n");
                }

                if (totalCount > maxRecords) {
                    result.append("... 还有 ").append(totalCount - maxRecords).append(" 条消息");
                }

                return result.toString().trim();
            }

            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 解析合并消息中的文件路径
     * @param msgDTO 消息DTO
     * @param xmlContent XML内容
     * @param decryptMsgDir 解密消息目录
     */
    private static void parseCombineFilePaths(MsgDTO msgDTO, String xmlContent, String decryptMsgDir) {
        try {
            List<String> filePaths = new ArrayList<>();
            List<String> thumbnailPaths = new ArrayList<>();

            // 检查是否是新格式的recordinfo结构
            if (xmlContent.contains("<recordinfo>")) {
                // 使用正则表达式提取dataitem节点
                Pattern dataitemPattern = Pattern.compile("<dataitem[^>]*>(.*?)</dataitem>", Pattern.DOTALL);
                Matcher dataitemMatcher = dataitemPattern.matcher(xmlContent);

                while (dataitemMatcher.find()) {
                    String dataitemContent = dataitemMatcher.group(1);

                    // 提取dataid作为文件标识
                    String dataid = extractXmlValue(dataitemContent, "dataid");
                    String datafmt = extractXmlValue(dataitemContent, "datafmt");

                    if (dataid != null && "mp4".equals(datafmt)) {
                        // 尝试查找对应的视频文件
                        String videoPath = findVideoFileByDataId(dataid, decryptMsgDir);
                        if (videoPath != null) {
                            filePaths.add(videoPath);
                        } else {
                            // 如果找不到视频文件，尝试查找缩略图
                            String thumbPath = findThumbnailFileByDataId(dataid, decryptMsgDir);
                            if (thumbPath != null) {
                                thumbnailPaths.add(thumbPath);
                            }
                        }
                    }
                }
            }

            // 设置到msgDTO中
            if (!filePaths.isEmpty()) {
                msgDTO.setCombineFilePaths(filePaths);
            }
            if (!thumbnailPaths.isEmpty()) {
                msgDTO.setCombineThumbnailPaths(thumbnailPaths);
            }
        } catch (Exception e) {
            // 解析失败时不影响基本功能
            log.warn("解析合并消息文件路径失败", e);
        }
    }

    /**
     * 根据dataid查找对应的视频文件
     * @param dataid 数据ID
     * @param decryptMsgDir 解密消息目录
     * @return 视频文件路径，如果找不到返回null
     */
    private static String findVideoFileByDataId(String dataid, String decryptMsgDir) {
        try {
            // 在Video目录中查找包含dataid的mp4文件
            String videoDir = decryptMsgDir + "\\pugwoo\\Video";
            if (!FileUtils.isExist(videoDir)) {
                return null;
            }

            List<String> allFiles = FileUtils.listFiles(videoDir);
            for (String filePath : allFiles) {
                if (filePath.toLowerCase().endsWith(".mp4") && filePath.contains(dataid)) {
                    if (FileUtils.isExist(filePath)) {
                        return filePath;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("查找视频文件失败: dataid=" + dataid, e);
        }
        return null;
    }

    /**
     * 根据dataid查找对应的缩略图文件
     * @param dataid 数据ID
     * @param decryptMsgDir 解密消息目录
     * @return 缩略图文件路径，如果找不到返回null
     */
    private static String findThumbnailFileByDataId(String dataid, String decryptMsgDir) {
        try {
            // 在Video目录中查找包含dataid的jpg文件
            String videoDir = decryptMsgDir + "\\pugwoo\\Video";
            if (!FileUtils.isExist(videoDir)) {
                return null;
            }

            List<String> allFiles = FileUtils.listFiles(videoDir);
            for (String filePath : allFiles) {
                if (filePath.toLowerCase().endsWith(".jpg") && filePath.contains(dataid)) {
                    if (FileUtils.isExist(filePath)) {
                        return filePath;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("查找缩略图文件失败: dataid=" + dataid, e);
        }
        return null;
    }

    /**
     * 解析新格式的recordinfo结构
     * @param xmlContent XML内容
     * @return 格式化的聊天记录
     */
    private static String parseRecordInfoFormat(String xmlContent) {
        try {
            List<String> records = new ArrayList<>();

            // 提取标题和描述
            String title = extractXmlValue(xmlContent, "title");
            String desc = extractXmlValue(xmlContent, "desc");

            // 提取datalist中的count属性
            Pattern datalistPattern = Pattern.compile("<datalist[^>]*count=\"(\\d+)\"[^>]*>", Pattern.DOTALL);
            Matcher datalistMatcher = datalistPattern.matcher(xmlContent);
            int totalCount = 0;
            if (datalistMatcher.find()) {
                totalCount = Integer.parseInt(datalistMatcher.group(1));
            }

            // 使用正则表达式提取dataitem节点
            Pattern dataitemPattern = Pattern.compile("<dataitem[^>]*>(.*?)</dataitem>", Pattern.DOTALL);
            Matcher dataitemMatcher = dataitemPattern.matcher(xmlContent);

            int count = 0;
            int maxRecords = 5; // 最多显示5条记录

            while (dataitemMatcher.find() && count < maxRecords) {
                String dataitemContent = dataitemMatcher.group(1);

                String sourcename = extractXmlValue(dataitemContent, "sourcename");
                String sourcetime = extractXmlValue(dataitemContent, "sourcetime");
                String duration = extractXmlValue(dataitemContent, "duration");
                String datafmt = extractXmlValue(dataitemContent, "datafmt");

                if (sourcename != null) {
                    String timeDisplay = "";
                    if (sourcetime != null && !sourcetime.trim().isEmpty()) {
                        timeDisplay = "[" + sourcetime + "] ";
                    }

                    String contentDisplay;
                    if ("mp4".equals(datafmt) && duration != null) {
                        contentDisplay = "[视频 " + duration + "秒]";
                    } else if (datafmt != null) {
                        contentDisplay = "[" + datafmt.toUpperCase() + "文件]";
                    } else {
                        contentDisplay = "[媒体文件]";
                    }

                    records.add(timeDisplay + sourcename + ": " + contentDisplay);
                    count++;
                }
            }

            if (!records.isEmpty()) {
                StringBuilder result = new StringBuilder();

                // 添加标题信息
                if (title != null && !title.trim().isEmpty()) {
                    result.append("标题: ").append(title).append("\n");
                }

                // 添加记录
                for (String record : records) {
                    result.append(record).append("\n");
                }

                if (totalCount > maxRecords) {
                    result.append("... 还有 ").append(totalCount - maxRecords).append(" 条消息");
                }

                return result.toString().trim();
            }

            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从XML中提取指定标签的值
     * @param xml XML内容
     * @param tagName 标签名
     * @return 标签值
     */
    private static String extractXmlValue(String xml, String tagName) {
        if (xml == null || tagName == null) {
            return null;
        }

        try {
            // 处理CDATA格式
            Pattern cdataPattern = Pattern.compile("<" + tagName + "><!\\[CDATA\\[(.*?)\\]\\]></" + tagName + ">", Pattern.DOTALL);
            Matcher cdataMatcher = cdataPattern.matcher(xml);
            if (cdataMatcher.find()) {
                return cdataMatcher.group(1);
            }

            // 处理普通格式
            Pattern pattern = Pattern.compile("<" + tagName + ">(.*?)</" + tagName + ">", Pattern.DOTALL);
            Matcher matcher = pattern.matcher(xml);
            if (matcher.find()) {
                return matcher.group(1);
            }

            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 判断内容是否是这样的随机字符串：03467d3a39cbd209339a18fa2c0dba63
     */
    private static boolean isRandomStr(String str) {
        if (str == null || str.length() != 32) {
            return false;
        }
        for (char c : str.toCharArray()) {
            if (!(Character.isDigit(c) || c >= 'a' && c <= 'f')) {
                return false;
            }
        }
        return true;
    }
}
