package com.pugwoo.weixin_msg.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("Contact")
public class ContactDO {

    @Column(value = "UserName", isKey = true)
    private String userName;

    @Column(value = "Alias")
    private String alias;

    @Column(value = "EncryptUserName")
    private String encryptUserName;

    @Column(value = "DelFlag")
    private Integer delFlag;

    @Column(value = "Type")
    private Integer type;

    @Column(value = "VerifyFlag")
    private Integer verifyFlag;

    @Column(value = "Reserved1")
    private String reserved1;

    @Column(value = "Reserved2")
    private String reserved2;

    @Column(value = "Reserved3")
    private String reserved3;

    @Column(value = "Reserved4")
    private String reserved4;

    @Column(value = "Remark")
    private String remark;

    @Column(value = "NickName")
    private String nickName;

    @Column(value = "LabelIDList")
    private String labelIDList;

    @Column(value = "DomainList")
    private String domainList;

    @Column(value = "ChatRoomType")
    private Integer chatRoomType;

    @Column(value = "PYInitial")
    private String pYInitial;

    @Column(value = "QuanPin")
    private String quanPin;

    @Column(value = "RemarkPYInitial")
    private String remarkPYInitial;

    @Column(value = "RemarkQuanPin")
    private String remarkQuanPin;

    @Column(value = "BigHeadImgUrl")
    private String bigHeadImgUrl;

    @Column(value = "SmallHeadImgUrl")
    private String smallHeadImgUrl;

    @Column(value = "HeadImgMd5")
    private String headImgMd5;

    @Column(value = "ChatRoomNotify")
    private String chatRoomNotify;

    @Column(value = "Reserved5")
    private String reserved5;

    @Column(value = "Reserved6")
    private String reserved6;

    @Column(value = "Reserved7")
    private String reserved7;

    @Column(value = "ExtraBuf")
    private byte[] extraBuf;

    @Column(value = "Reserved8")
    private String reserved8;

    @Column(value = "Reserved9")
    private String reserved9;

    @Column(value = "Reserved10")
    private String reserved10;

    @Column(value = "Reserved11")
    private String reserved11;

}