package com.pugwoo.weixin_msg.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 消息差别类型
 */
@Getter
public enum MsgDiffTypeEnum {

    ONLY_LEFT("ONLY_LEFT", "只有左侧的消息有"),

    ONLY_RIGHT("ONLY_RIGHT", "只有右侧的消息有"),

    ;

    final private String code;
    final private String name;

    MsgDiffTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MsgDiffTypeEnum getByCode(String code) {
        for (MsgDiffTypeEnum e : MsgDiffTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        MsgDiffTypeEnum e = getByCode(code);
        return e == null ? (code == null ? "" : code) : e.getName();
    }

}